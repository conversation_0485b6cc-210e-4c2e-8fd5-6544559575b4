using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Animations;
using System.Collections;

[System.Serializable]
public class SyncedAnimations
{
    [System.Serializable]
    public class SyncedAnim
    {
        public Animator m_animator;
        public AnimationClip m_clip;
    }

    public SyncedAnim[] m_syncedAnims;

    private struct PlayableInfo
    {
        public PlayableGraph m_graph;
        public AnimationClipPlayable m_clip;
        public bool m_isLooping;
        public double m_duration;

        public bool IsFinished()
        {
            return !m_isLooping && m_clip.GetTime() >= m_duration;
        }
    }

    private PlayableInfo[] m_playableInfos = null;

    public void Play(MonoBehaviour _mono)
    {
        m_playableInfos = new PlayableInfo[m_syncedAnims.Length];

        for (int i = 0; i < m_syncedAnims.Length; i++)
        {
            m_playableInfos[i].m_graph = PlayableGraph.Create($"Graph_{i}");
            var playableOutput = AnimationPlayableOutput.Create(m_playableInfos[i].m_graph, $"Output_{i}", m_syncedAnims[i].m_animator);
            m_playableInfos[i].m_clip = AnimationClipPlayable.Create(m_playableInfos[i].m_graph, m_syncedAnims[i].m_clip);
            playableOutput.SetSourcePlayable(m_playableInfos[i].m_clip);
            m_playableInfos[i].m_isLooping = m_syncedAnims[i].m_clip.isLooping;
            m_playableInfos[i].m_duration = m_syncedAnims[i].m_clip.length;

            m_playableInfos[i].m_graph.Play();

            if(!m_playableInfos[i].m_isLooping)
            {
                _mono.StartCoroutine(Co_CheckFinished(m_playableInfos[i]));
            }
        }
    }

    public void Stop()
    {
        if (m_playableInfos != null)
        {
            foreach (var playableInfo in m_playableInfos)
            {
                if (playableInfo.m_graph.IsValid())
                {
                    playableInfo.m_graph.Stop();
                    playableInfo.m_graph.Destroy();
                }
            }
        }
    }

    private IEnumerator Co_CheckFinished(PlayableInfo _playableInfo)
    {
        while(_playableInfo.m_graph.IsValid())
        {
            if (_playableInfo.IsFinished())
            {
                _playableInfo.m_graph.Stop();
                _playableInfo.m_graph.Destroy();
            }

            yield return null;
        }  
    }
}

