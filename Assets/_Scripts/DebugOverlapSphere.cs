using System.Collections.Generic;
using UnityEngine;

public class DebugOverlapSphere : MonoBehaviour
{
    public float m_radius = 30;
    public bool m_syncPhysics = false;
    public bool m_catchTriggers = false;
    public bool m_useSafeTest = false;
    public bool m_resetBroadphase = false;
    public bool m_wakeBodies = false;

    public static Collider[] OverlapSphereSafe(Vector3 center, float radius, int layerMask = ~0, QueryTriggerInteraction qti = QueryTriggerInteraction.Ignore)
    {
        var hits = Physics.OverlapBox(center, new Vector3(radius, radius, radius), Quaternion.identity, layerMask, qti);
        var sphereHits = new List<Collider>();
        float r2 = radius * radius;
        for (int i = 0; i < hits.Length; i++)
        {
            var c = hits[i];
            Vector3 cp = c.ClosestPoint(center);
            if ((cp - center).sqrMagnitude <= r2 + 1e-5f) // tiny epsilon for numeric jitter
                sphereHits.Add(c);
        }
        return sphereHits.ToArray();
    }
    
    void OnDrawGizmos()
    {
        if (m_resetBroadphase)
        {
            var worldCenter = (GlobalData.c_terrainMin + GlobalData.c_terrainMax) * .5f;
            var worldSize = new Vector3(1024 * 2, 500, 1024 * 2); 
            Physics.RebuildBroadphaseRegions(new Bounds(worldCenter, worldSize), 8);
        }
        if (m_wakeBodies)
        {
            foreach (var rb in FindObjectsOfType<Rigidbody>()) rb.WakeUp();
        }
        if (m_syncPhysics)
            Physics.SyncTransforms();
        Gizmos.color = Color.white;
        Gizmos.DrawWireSphere(transform.position, m_radius);
        var colliders = m_useSafeTest
            ? OverlapSphereSafe(transform.position, m_radius, -1, m_catchTriggers ? QueryTriggerInteraction.Collide : QueryTriggerInteraction.Ignore)
            : Physics.OverlapSphere(transform.position, m_radius, -1, m_catchTriggers ? QueryTriggerInteraction.Collide : QueryTriggerInteraction.Ignore);
        foreach (var collider in colliders)
        {
            var rb = collider.attachedRigidbody;
            if (rb == null) Gizmos.color = Color.red;
            else Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(collider.transform.position, .5f);
            Gizmos.DrawLine(transform.position, collider.transform.position);
        }
    }
}
