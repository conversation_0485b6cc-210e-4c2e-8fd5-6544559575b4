using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using UnityEditor;
using UnityEngine;
using Application = UnityEngine.Application;
using Object = UnityEngine.Object;
using static KitBashOptimiseController;
using OR = OptimiserReport;

public class KitBashOptimiser
{
    private const string CUSTOM_ICON_PATH = "Packages/com.unity.timeline/Editor/StyleSheets/Images/Shared/TimelineWarning.png";
    private static Dictionary<Object, string> s_toSave = new();
    private static HashSet<GameObject> s_temporaryPrefabs = new();
    private static int s_InProgressCount = 0;
    private Dictionary<GameObject, string> m_folderPaths;
    private Configuration m_config;
    private Dictionary<string, Object> m_oldOptimizedAssets;

    private static Dictionary<string, Stopwatch> s_timers = new();
    private static Dictionary<string, float> s_times = new();

    private static Dictionary<string, string> s_descriptions = new()
    {
        
    };
    
    public static void StartSection(string name)
    {
        OR.StartSection(name, s_descriptions.GetValueOrDefault(name, $" It's {name}! Need I say more?"));
    }

    public static void EndSection(string name)
    {
        OR.EndSection(name);
    }

    public static bool ShouldRun(Configuration config)
    {
        return ShouldProcessDirectory(config);
    }

    public static KitBashOptimiser Run(Configuration config)
    {
        var manager = new KitBashOptimiser { m_config = config };
        manager.Optimise();
        return manager;
    }

    private static bool ShouldProcessDirectory(Configuration config)
    {
        if (!Directory.Exists(config.directoryTo))
            return true;
        
        var infoFile = Path.Combine(config.directoryFrom, "optimiser.info");
        if (!File.Exists(infoFile))
            OR.Log($"{Path.GetFileName(config.directoryTo)}: Not previously run. Optimising.");
        else if (!forceUpdate)
        {
            var lines = File.ReadAllLines(infoFile);
            var dirty = false;
            foreach (var line in lines)
            {
                var parts = line.Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length < 3 || parts[2] != parts[1])
                {
                    dirty = true;
                    break;
                }
            }
            if (!dirty)
                return false;
        }
        return true;
    }

    private void Optimise()
    {
        s_InProgressCount++;
        var startTime = DateTime.Now.Ticks;
        var objs = GetAllGameObjects(m_config);
        List<string> paths;
        
        try
        {
            ProcessMeshesAndBatching(objs);
            StartSection("Write prefabs");
            paths = WriteAllPrefabs(objs);
            EndSection("Write prefabs");
            StartSection("Write dependencies");
            WriteInfoFile(objs);
            EndSection("Write dependencies");
        }
        catch (Exception e)
        {
            HandleOptimisationError(e, objs);
            s_InProgressCount--;
            return;
        }
        StartSection("Cleanup");
        CleanupFolder(paths);
        EndSection("Cleanup");
        s_InProgressCount--;
    }

    private void ProcessMeshesAndBatching(List<GameObject> objs)
    {
        int totOldRends = 0, totNewRends = 0;

        var materialPath = GetMaterialPath();
        if (Directory.Exists(materialPath))
            Directory.Delete(materialPath, true);
        Directory.CreateDirectory(materialPath);
        
        var meshPath = GetMeshPath();
        if (Directory.Exists(meshPath))
            Directory.Delete(meshPath, true);
        Directory.CreateDirectory(meshPath);
        
        var atlasPath = GetAtlasPath();
        if (Directory.Exists(atlasPath))
            Directory.Delete(atlasPath, true);
        Directory.CreateDirectory(atlasPath);
        
        StartSection("Atlassing");
        MeshAtlasser.Start(objs, materialPath, meshPath, atlasPath, m_config.atlasName);
        EndSection("Atlassing");
        
        foreach (var obj in objs)
        {
            try
            {
                StartSection("Batching");
                ProcessSingleObject(obj, ref totOldRends, ref totNewRends);
                EndSection("Batching");
            }
            catch (Exception e)
            {
                LogPrefabError(obj, e);
            }
        }
    }

    private void ProcessSingleObject(GameObject obj, ref int totOldRends, ref int totNewRends)
    {
        PrefabBatcher.Run(obj, this);
        var (oldCount, newCount) = PrefabBatcher.Me.GetRendererCount();
        totOldRends += oldCount;
        totNewRends += newCount;
    }

    private List<string> WriteAllPrefabs(List<GameObject> objs)
    {
        if (objs == null || objs.Count == 0) return new();

        StartSection("Save other assets");
        SavePendingAssets();
        EndSection("Save other assets");
        var workItems = new List<(GameObject go, string dstPath, string srcGuid, string dstGuid)>();
        string originalPath = m_config.directoryTo;

        foreach (var obj in objs)
        {
            string srcPath = $"{m_config.directoryFrom}/{GetPathRelFolder(obj)}";
            string dstPath = Path.ChangeExtension($"{m_config.directoryTo}/{GetPathRelFolder(obj)}", ".prefab");
            string srcGuid = AssetDatabase.AssetPathToGUID(srcPath);
            string dstGuid = AssetDatabase.AssetPathToGUID(dstPath);

            workItems.Add((obj, dstPath, srcGuid, dstGuid));
        }
        if (!AssetDatabase.IsValidFolder(originalPath))
            AssetDatabase.CreateFolder(Path.GetDirectoryName(originalPath), Path.GetFileName(originalPath));

        AssetDatabase.StartAssetEditing();
        try
        {
            foreach (var (go, dstPath, _, _) in workItems)
            {
                SavePrefabAsset(go, dstPath);
            }
            AssetDatabase.SaveAssets();
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        var assetMap = new Dictionary<string, string>();
        foreach (var (_, dstPath, srcGuid, dstGuid) in workItems)
        {
            var newGuid = AssetDatabase.AssetPathToGUID(dstPath);
            if (!string.IsNullOrEmpty(newGuid))
                assetMap[srcGuid] = newGuid;
            if (!string.IsNullOrEmpty(dstGuid))
                assetMap[dstGuid] = newGuid;
        }

        var targets = new List<Object>();
        targets.AddRange(m_config.targetPrefabs);
        targets.AddRange(m_config.targetScenes);

        if (targets.Count > 0 && assetMap.Count > 0)
        {
            int remapped = AssetRemapperWindow.RemapAssetReferences(assetMap, targets);
            OR.Log($"Remapped {remapped} refs in {targets.Count} assets");
        }
        
        var icon = AssetDatabase.LoadAssetAtPath<Texture2D>(CUSTOM_ICON_PATH);
        if (icon != null)
        {
            AssetDatabase.StartAssetEditing();
            try
            {
                foreach (var (_, dstPath, _, _) in workItems)
                {
                    try
                    {
                        var obj = AssetDatabase.LoadAssetAtPath<Object>(dstPath);
                        EditorGUIUtility.SetIconForObject(obj, icon);
                    }
                    catch (Exception e)
                    {
                        OR.LogWarning($"Icon set failed for {dstPath}: {e.Message}");
                    }
                }
                AssetDatabase.SaveAssets();
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            }
        }
        return workItems.Select(x => x.dstPath).ToList();
    }

    private void SavePrefabAsset(GameObject obj, string path)
    {
        try
        {
            Directory.CreateDirectory(Path.GetDirectoryName(path));
            PrefabUtility.SaveAsPrefabAssetAndConnect(obj, path, InteractionMode.AutomatedAction);
            AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceSynchronousImport);
        }
        finally
        {
            Object.DestroyImmediate(obj);
            s_temporaryPrefabs.Remove(obj);
        }
    }
    
    private void CleanupFolder(List<string> paths)
    {
        var usedPaths = new HashSet<string>();
        foreach (var path in paths)
        {
            foreach (var dep in AssetDatabase.GetDependencies(path, true))
            {
                string depNormalized = dep.Replace("\\", "/");
                if (depNormalized.StartsWith(m_config.directoryTo.Replace("\\", "/")))
                    usedPaths.Add(depNormalized);
            }
        }

        AssetDatabase.StartAssetEditing();
        try
        {
            CleanupFolderRecursive(usedPaths, m_config.directoryTo);
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
        }
    }

    private static bool CleanupFolderRecursive(HashSet<string> usedPaths, string folderPath)
    {
        if (string.Equals(Path.GetFileName(folderPath), "Editor", StringComparison.OrdinalIgnoreCase))
            return true;

        bool nonEmpty = false;
        var dirInfo = new DirectoryInfo(folderPath);
        foreach (var fsi in dirInfo.EnumerateFileSystemInfos())
        {
            if (fsi.Attributes.HasFlag(FileAttributes.Directory))
            {
                var subFolder = fsi.FullName;
                bool childLeft = CleanupFolderRecursive(usedPaths, subFolder);
                if (!childLeft)
                    DeleteAsset(subFolder);
                else
                    nonEmpty = true;
            }
            else
            {
                if (string.Equals(fsi.Extension, ".meta", StringComparison.OrdinalIgnoreCase))
                    continue;
                var assetPath = AbsolutePathToAssetPath(fsi.FullName).Replace("\\", "/");
                if (!usedPaths.Contains(assetPath))
                    DeleteAsset(fsi.FullName);
                else
                    nonEmpty = true;
            }
        }
        return nonEmpty;
    }
    
    private static void DeleteAsset(string path)
    {
        FileUtil.DeleteFileOrDirectory(path);
        FileUtil.DeleteFileOrDirectory($"{path}.meta");
    }

    private static string AbsolutePathToAssetPath(string absolutePath)
    {
        string assetPath = absolutePath.Replace("\\", "/");
        string projectPath = Application.dataPath.Replace("\\", "/");
        projectPath = projectPath[..^"/Assets".Length];
        if (assetPath.StartsWith(projectPath))
            return assetPath[(projectPath.Length + 1)..]; // +1 to remove the slash
        return assetPath;
    }


    private void WriteInfoFile(List<GameObject> objs)
    {
        var infoFile = Path.Combine(m_config.directoryFrom, "optimiser.info");
        var dependencies = new HashSet<string>();
        foreach (var obj in objs)
        {
            var prefPath = GetFromPath(obj).Replace(Application.dataPath, "Assets");
            dependencies.Add(prefPath);
            var deps = AssetDatabase.GetDependencies(prefPath, true);
            foreach (var dep in deps)
            {
                if (!IsRelevantAsset(dep))
                    continue;
                dependencies.Add(dep);
            }
        }
        
        var sortedDependencies = new List<string>();
        AddMultiDep(new List<string>(dependencies), sortedDependencies);
        sortedDependencies.Sort((a, b) => string.Compare(a, b, StringComparison.Ordinal));
        File.WriteAllLines(infoFile, sortedDependencies);
    }
    
    private static void AddMultiDep(List<string> _paths, List<string> _deps)
    {
        GetMultiContentHash(_paths, (path, hash) => _deps.Add($"{path.Replace('\\', '/')},{hash},{hash}"));
    }

    private static void GetMultiContentHash(List<string> _paths, Action<string, string> Callback)
    {
        var jobs = new Dictionary<string, (JobHandle, FileContentNormaliserJob)>();
        foreach (var path in _paths)
        {
            var rawBytes = File.ReadAllBytes(path);
            var job = new FileContentNormaliserJob(rawBytes);
            var handle = job.Schedule();
            jobs[path] = (handle, job);
        }

        foreach (var (path, (handle, job)) in jobs)
        {
            handle.Complete();
            var ul = job.GetResult();
            Callback(path, ul.ToString("x"));
        }
    }

    public static string GetContentHash(string _path, bool _relative = true)
    {
        if (_relative)
            _path = Path.Combine(Application.dataPath, Path.GetRelativePath("Assets", _path));

        var rawBytes = File.ReadAllBytes(_path);
        var job = new FileContentNormaliserJob(rawBytes);
        job.Schedule().Complete();
        var ul = job.GetResult();
        
        return ul.ToString("x");
    }

    [BurstCompile]
    private struct FileContentNormaliserJob : IJob
    {
        const byte CR = (byte)'\r'; 
        const byte LF = (byte)'\n';
        [ReadOnly] private NativeArray<byte> input;
        [WriteOnly] private NativeArray<ulong> hash;
        
        public FileContentNormaliserJob(byte[] _input)
        {
            var hasBOM = _input.Length >= 3 && _input[0] == 0xEF && _input[1] == 0xBB && _input[2] == 0xBF;
            var length = hasBOM ? _input.Length - 3 : _input.Length;
            input = new NativeArray<byte>(length, Allocator.TempJob);
            NativeArray<byte>.Copy(_input, hasBOM ? 3 : 0, input, 0, length);
            hash = new(1, Allocator.TempJob);
        }

        public void Execute()
        {
            var output = new NativeList<byte>(input.Length, Allocator.Temp);
            for (var i = 0; i < input.Length; ++i)
            {
                var ch = input[i];
                if (ch == CR)
                    continue;
                if (i > 0 && input[i - 1] == CR && ch != LF)
                    output.Add(LF);
                output.Add(ch);
            }

            var ui2 = UnsafeUtilities.xxHashList(output);
            hash[0] = (ulong)ui2.x << 32 | ui2.y;
        }

        public ulong GetResult()
        {
            var result = hash[0];
            input.Dispose();
            hash.Dispose();
            return result;
        }
    }

    public static string GetRelativePath(string absolute) => Path.Combine("Assets", Path.GetRelativePath(Application.dataPath, absolute));
    
    public string GetFromPath(GameObject obj) => Path.Combine(m_config.directoryFrom, GetPathRelFolder(obj));
    
    public string GetMeshPath(GameObject obj = null) => Path.Combine((obj == null ? m_config.directoryTo : GetToFolder(obj)), "Meshes");
    
    public string GetMaterialPath() => Path.Combine(m_config.directoryTo, "Materials");
    
    public string GetAtlasPath() => Path.Combine(m_config.directoryTo, "Atlases");
    
    private string GetToFolder(GameObject obj) => Path.Combine(m_config.directoryTo, Path.ChangeExtension(GetPathRelFolder(obj), null));

    public string GetPathRelFolder(GameObject obj) => m_folderPaths[obj];

    public static void SaveAsset(Object asset, string path)
    {
        if (!s_toSave.TryAdd(asset, path))
            s_toSave[asset] = path;
    }

    private void SavePendingAssets()
    {
        AssetDatabase.StartAssetEditing();
        try
        {
            foreach (var (obj, path) in s_toSave)
            {
                var assetPath = GetRelativePath(path);
                if (AssetDatabase.Contains(obj))
                {
                    var oldPath = AssetDatabase.GetAssetPath(obj);
                    OR.LogWarning($"Moving {obj} from {oldPath} to {assetPath}.");
                    AssetDatabase.MoveAsset(oldPath, assetPath);
                    continue;
                }
                Directory.CreateDirectory(Path.GetDirectoryName(path));
                AssetDatabase.CreateAsset(obj, assetPath);
            }
            AssetDatabase.SaveAssets();
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
        }
        s_toSave.Clear();
    }

    
    public static bool IsInProgress()
    {
        return s_InProgressCount > 0;
    }

    private void HandleOptimisationError(Exception e, List<GameObject> objs)
    {
        OR.LogException(e);
        foreach (var obj in objs)
            Object.DestroyImmediate(obj);
        s_temporaryPrefabs.Clear();
    }

    private void LogPrefabError(GameObject obj, Exception e)
    {
        OR.LogError($"Issue with prefab at {GetFromPath(obj)}! See next error for details!", obj);
        OR.LogException(e);
    }

    private List<GameObject> GetAllGameObjects(Configuration config)
    {
        m_folderPaths = new Dictionary<GameObject, string>();
        var objects = new List<GameObject>();
        var fromPath = config.directoryFrom;

        var prefabNames = Directory.GetFiles(fromPath, "*.prefab", SearchOption.AllDirectories);
        var fbxNames = Directory.GetFiles(fromPath, "*.fbx", SearchOption.AllDirectories);
        var allNames = new HashSet<string>(prefabNames);
        foreach (var name in fbxNames)
        {
            if (allNames.Contains(name.Replace(".fbx", ".prefab")))
                continue;
            allNames.Add(name);
        }

        foreach (var file in allNames)
            ProcessPrefabFile(file, fromPath, objects);

        return objects;
    }

    private void ProcessPrefabFile(string file, string fromPath, List<GameObject> objects)
    {
        var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(GetRelativePath(file));
        if (CheckMissingScripts(prefab))
            return;

        var instance = (GameObject)PrefabUtility.InstantiatePrefab(prefab);
        PrefabUtility.UnpackPrefabInstance(instance, PrefabUnpackMode.Completely, InteractionMode.AutomatedAction);
        DestroyURPObjects(instance.transform);
        foreach (var dh in instance.GetComponentsInChildren<DecorationHolder>(true))
            dh.m_externalMeshRenderers.Clear();
        s_temporaryPrefabs.Add(instance);
        objects.Add(instance);
        var relPath = Path.GetRelativePath(fromPath, file);
        m_folderPaths.Add(instance, relPath);
    }

    private static void DestroyURPObjects(Transform root)
    {
        for (int i = root.childCount - 1; i >= 0; --i)
        {
            var child = root.GetChild(i);
            if (child.name.Contains("URP"))
                Object.DestroyImmediate(child.gameObject);
            else
                DestroyURPObjects(child);
        }
    }

    private bool CheckMissingScripts(GameObject prefab)
    {
        bool hasMissing = false;
        Transform[] transforms = prefab.GetComponentsInChildren<Transform>(true);

        foreach (Transform transform in transforms)
        {
            if (CheckTransformForMissingScripts(transform, prefab))
                hasMissing = true;
        }

        return hasMissing;
    }

    private bool CheckTransformForMissingScripts(Transform transform, GameObject prefab)
    {
        GameObject go = transform.gameObject;
        int missingCount = GameObjectUtility.GetMonoBehavioursWithMissingScriptCount(go);

        if (missingCount > 0)
        {
            OR.LogWarning($"{prefab.name} is missing {missingCount} scripts on {go.name}. It will not be processed.", go);
            return true;
        }

        return false;
    }

    public static string ColourName(DecorationHolder holder) => ColourName(holder.m_channel);
    public static string ColourName(int channel) => ColourName((DecorationHolder.EChannel)channel);
    public static string ColourName(DecorationHolder.EChannel channel)
    {
        return channel switch {
            DecorationHolder.EChannel.R => "_R",
            DecorationHolder.EChannel.G => "_G",
            DecorationHolder.EChannel.B => "_B",
            _ => "_A"};
    }

    public static string BlendName(DecorationHolder holder) => BlendName(holder.m_channel);
    public static string BlendName(int channel) => BlendName((DecorationHolder.EChannel)channel);
    public static string BlendName(DecorationHolder.EChannel channel)
    {
        return channel switch {
            DecorationHolder.EChannel.R => "_R_ON_OFF",
            DecorationHolder.EChannel.G => "_G_Blend",
            DecorationHolder.EChannel.B => "_B_Blend",
            _ => "_A_Blend"};
    }
}

