using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class ORRendererTab : OptimiserReportTab
{
    private enum SortMode
    {
        PrefabName,
        Reduction,
        ProportionalReduction,
        Remaining
    }

    private Dictionary<string, (int originalCount, int optimisedCount)> _rendererData;
    private OptimiserReport _cachedReport;
    private Vector2 _scrollPosition;
    private SortMode _sortMode = SortMode.ProportionalReduction;
    private bool _sortAscending;

    private void OnEnable()
    {
        m_title = "Renderers";
    }

    public override void Draw(OptimiserReport report, OptimiserReportEditor editor)
    {
        if (report != _cachedReport)
        {
            GatherRendererData(report);
            _cachedReport = report;
        }

        if (_rendererData == null || _rendererData.Count == 0)
        {
            EditorGUILayout.HelpBox("Renderer data not available in this report.", MessageType.Info);
            return;
        }

        DrawToolbar();
        DrawRendererList();
    }

    private void DrawToolbar()
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);

        string arrow = _sortAscending ? "▲" : "▼";
        
        if (GUILayout.Button($"Reduction (%) {(_sortMode == SortMode.ProportionalReduction ? arrow : "")}", EditorStyles.toolbarButton, GUILayout.Width(130)))
        {
            if (_sortMode == SortMode.ProportionalReduction) _sortAscending = !_sortAscending;
            else _sortAscending = false;
            _sortMode = SortMode.ProportionalReduction;
        }

        string reductionLabel = $"Reduction {(_sortMode == SortMode.Reduction ? arrow : "")}";
        if (GUILayout.Button(new GUIContent(reductionLabel), EditorStyles.toolbarButton))
        {
            if (_sortMode == SortMode.Reduction) _sortAscending = !_sortAscending;
            else _sortAscending = false;
            _sortMode = SortMode.Reduction;
        }
        
        string remainingLabel = $"Remaining {(_sortMode == SortMode.Remaining ? arrow : "")}";
        if (GUILayout.Button(new GUIContent(remainingLabel), EditorStyles.toolbarButton))
        {
            if (_sortMode == SortMode.Remaining) _sortAscending = !_sortAscending;
            else _sortAscending = false;
            _sortMode = SortMode.Remaining;
        }
        
        string nameLabel = $"Prefab Name {(_sortMode == SortMode.PrefabName ? arrow : "")}";
        if (GUILayout.Button(new GUIContent(nameLabel), EditorStyles.toolbarButton))
        {
            if (_sortMode == SortMode.PrefabName) _sortAscending = !_sortAscending;
            else _sortAscending = true;
            _sortMode = SortMode.PrefabName;
        }

        GUILayout.FlexibleSpace();
        
        EditorGUILayout.EndHorizontal();
    }

    private void DrawRendererList()
    {
        EditorGUILayout.BeginHorizontal(GUI.skin.box);
        GUILayout.Label("Optimised Prefab Path", EditorStyles.boldLabel);
        GUILayout.Label("Original", EditorStyles.boldLabel, GUILayout.Width(80));
        GUILayout.Label("Optimised", EditorStyles.boldLabel, GUILayout.Width(80));
        GUILayout.Label("Change", EditorStyles.boldLabel, GUILayout.Width(80));
        GUILayout.Label("Change (%)", EditorStyles.boldLabel, GUILayout.Width(80));
        EditorGUILayout.EndHorizontal();

        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

        var sortedData = GetSortedData();

        foreach (var (path, (originalCount, optimisedCount)) in sortedData)
        {
            EditorGUILayout.BeginHorizontal();

            EditorGUILayout.LabelField(new GUIContent(path, path));

            EditorGUILayout.LabelField(originalCount.ToString(), GUILayout.Width(80));
            EditorGUILayout.LabelField(optimisedCount.ToString(), GUILayout.Width(80));

            int delta = originalCount - optimisedCount;
            var style = new GUIStyle(EditorStyles.label);
            style.normal.textColor = delta switch
            {
                > 0 => new Color(0.5f, 1f, 0.5f),
                < 0 => Color.red,
                _ => style.normal.textColor
            };

            EditorGUILayout.LabelField(delta.ToString("-#;+#;0"), style, GUILayout.Width(80));
            
            float deltaFrac = 100f * delta / (originalCount + float.Epsilon);
            
            EditorGUILayout.LabelField($"{deltaFrac:-#;+#;0}%", style, GUILayout.Width(80));

            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.EndScrollView();
    }

    private IEnumerable<KeyValuePair<string, (int originalCount, int optimisedCount)>> GetSortedData()
    {
        var dataList = _rendererData.ToList();
        dataList.Sort((a, b) => _sortMode switch
        {
            SortMode.Reduction => CompareReduction(a, b),
            SortMode.ProportionalReduction => CompareRedFrac(a, b),
            SortMode.Remaining => CompareOptCount(a, b),
            _ => CompareName(a, b)
        });
        return dataList;

        int CompareReduction(KeyValuePair<string, (int, int)> a, KeyValuePair<string, (int, int)> b)
        {
            var redA = a.Value.Item1 - a.Value.Item2;
            var redB = b.Value.Item1 - b.Value.Item2;
            var comparison = redA.CompareTo(redB);
            if (comparison == 0)
                return CompareOptCount(a, b);
            return (_sortAscending ? 1 : -1) * redA.CompareTo(redB);
        }

        int CompareRedFrac(KeyValuePair<string, (int, int)> a, KeyValuePair<string, (int, int)> b)
        {
            var redA = (a.Value.Item1 - a.Value.Item2) / (a.Value.Item1 + float.Epsilon);
            var redB = (b.Value.Item1 - b.Value.Item2) / (b.Value.Item1 + float.Epsilon);
            var comparison = redA.CompareTo(redB);
            if (comparison == 0)
                return CompareOptCount(a, b);
            return (_sortAscending ? 1 : -1) * redA.CompareTo(redB);
        }
        
        int CompareOptCount(KeyValuePair<string, (int, int)> a, KeyValuePair<string, (int, int)> b, bool inv = false)
        {
            var redA = a.Value.Item2;
            var redB = b.Value.Item2;
            var comparison = redA.CompareTo(redB);
            if (comparison == 0)
                return CompareName(a, b);
            return (_sortAscending && !inv ? 1 : -1) * redA.CompareTo(redB);
        }
        
        int CompareName(KeyValuePair<string, (int, int)> a, KeyValuePair<string, (int, int)> b, bool inv = false)
        {
            return (_sortAscending && !inv ? 1 : -1) * string.Compare(a.Key, b.Key, StringComparison.InvariantCulture);
        }
    }

    private void GatherRendererData(OptimiserReport report)
    {
        _rendererData = new Dictionary<string, (int, int)>();

        foreach (var folderInfo in report.folderInfos)
        {
            var folderFrom = folderInfo.folderFrom;
            var folderTo = folderInfo.folderTo;

            if (string.IsNullOrEmpty(folderFrom) || string.IsNullOrEmpty(folderTo)) 
                continue;

            foreach (var guid in AssetDatabase.FindAssets("t:Prefab", new[] { folderTo }))
            {
                var prefabPathOptimised = AssetDatabase.GUIDToAssetPath(guid);

                var potentialOriginalPath = prefabPathOptimised.Replace(folderTo, folderFrom);
                string originalAssetPath = FindOriginalAssetPath(potentialOriginalPath);
                
                if (string.IsNullOrEmpty(originalAssetPath))
                {
                    Debug.LogWarning($"Could not find original asset for optimised prefab: {prefabPathOptimised}");
                    continue;
                }

                int optimisedCount = CountRenderersInAsset(prefabPathOptimised);
                int originalCount = CountRenderersInAsset(originalAssetPath);
                
                _rendererData[prefabPathOptimised] = (originalCount, optimisedCount);
            }
        }
    }

    private static string FindOriginalAssetPath(string potentialPath)
    {
        string basePath = Path.ChangeExtension(potentialPath, null);
        
        string prefabPath = basePath + ".prefab";
        if (File.Exists(prefabPath)) 
            return prefabPath;
        
        string fbxPath = basePath + ".fbx";
        return File.Exists(fbxPath) ? fbxPath : null;
    }
    
    private static int CountRenderersInAsset(string path)
    {
        var asset = AssetDatabase.LoadAssetAtPath<GameObject>(path);
        return asset == null ? 0 : asset.GetComponentsInChildren<Renderer>(true).Length;
    }
}