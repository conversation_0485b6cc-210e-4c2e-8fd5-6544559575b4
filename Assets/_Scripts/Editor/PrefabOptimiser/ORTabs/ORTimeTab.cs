using static ORTabUtilities;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

public class ORTimeTab : OptimiserReportTab
{
    private TreeViewState _treeState;
    private TimeTreeView _treeView;
    private TimeNode _rootTimeNode;
    private TimeNode _selectedNode;
    private OptimiserReport _cachedReport;

    // A color palette for the bar chart segments
    private static readonly Color[] BarColors =
    {
        new(0.2f, 0.6f, 0.9f), // Blue
        new(0.9f, 0.5f, 0.2f), // Orange
        new(0.3f, 0.8f, 0.4f), // Green
        new(0.9f, 0.3f, 0.3f), // Red
        new(0.6f, 0.4f, 0.8f), // Purple
        new(0.8f, 0.8f, 0.2f)  // Yellow
    };

    private void OnEnable()
    {
        m_title = "Time Breakdown";
    }

    public override void Draw(OptimiserReport report, OptimiserReportEditor editor)
    {
        if (report != _cachedReport)
        {
            BuildDataStructures(report);
            _cachedReport = report;
        }

        float halfWidth = Mathf.Floor(EditorGUIUtility.currentViewWidth * 0.5f) - 20;

        EditorGUILayout.BeginHorizontal();

        var panelGUIStyle = new GUIStyle(GUI.skin.box) { fixedWidth = halfWidth, fixedHeight = 0 };
        // Left Panel: TreeView
        GUILayout.BeginVertical(panelGUIStyle);
        Rect treeRect = GUILayoutUtility.GetRect(0, 10000, 15, _treeView.totalHeight + 15); 
        _treeView.OnGUI(treeRect);
        GUILayout.EndVertical();

        // Right Panel: Details and Bar Chart
        GUILayout.BeginVertical(panelGUIStyle);
        DrawDetailsAndBarChartPanel();
        GUILayout.EndVertical();

        EditorGUILayout.EndHorizontal();
    }
    
    /// <summary>
    /// Draws the right panel, showing task details and the composition bar chart.
    /// </summary>
    private void DrawDetailsAndBarChartPanel()
    {
        if (_selectedNode != null)
        {
            EditorGUILayout.LabelField("Time Details", EditorStyles.boldLabel);
            DrawDoubleLabel("Task:", _selectedNode.Name, EditorStyles.wordWrappedLabel);
            if (!string.IsNullOrEmpty(_selectedNode.Description))
            {
                DrawDoubleLabel("Description:", _selectedNode.Description, EditorStyles.wordWrappedLabel);
            }
            DrawDoubleLabel("Total Time:", $"{_selectedNode.Time:F2} s", EditorStyles.wordWrappedLabel);

            EditorGUILayout.Space(10);
            
            EditorGUILayout.LabelField("Task Composition", EditorStyles.boldLabel);
            
            // The new bar chart shows the composition of the selected node
            DrawCompositionBar(_selectedNode);
        }
        else
        {
            EditorGUILayout.HelpBox("Select a task in the tree to see its details.", MessageType.Info);
        }
    }
    
    private void DrawCompositionBar(TimeNode node)
    {
        Rect barRect = GUILayoutUtility.GetRect(25, 25, GUILayout.ExpandWidth(true));
        barRect.xMax -= 20;
        
        EditorGUI.DrawRect(barRect, new Color(0.15f, 0.15f, 0.15f));

        if (node.Time <= 0) return;

        if (node.Children.Count == 0)
        {
            EditorGUI.DrawRect(barRect, BarColors[0]);
            GUI.Label(barRect, $"{node.Name} ({node.Time:F2}s)", EditorStyles.whiteLabel);
            return;
        }

        var sortedChildren = node.Children.Values.OrderByDescending(c => c.Time).ToList();
        float currentX = barRect.x;

        for (int i = 0; i < sortedChildren.Count; i++)
        {
            var child = sortedChildren[i];
            float ratio = child.Time / node.Time;
            float segmentWidth = barRect.width * ratio;
            
            Rect segmentRect = new(currentX, barRect.y, segmentWidth, barRect.height);
            EditorGUI.DrawRect(segmentRect, BarColors[i % BarColors.Length]);
            
            currentX += segmentWidth;
        }
        
        EditorGUILayout.Space(5);
        for (int i = 0; i < sortedChildren.Count; i++)
        {
            var child = sortedChildren[i];
            float percentage = (child.Time / node.Time) * 100f;
            DrawLegend(BarColors[i % BarColors.Length], $"{child.Name} - {child.Time:F2}s ({percentage:F1}%)");
        }
    }
    
    private void DrawLegend(Color color, string label)
    {
        EditorGUILayout.BeginHorizontal();
        var colorRect = GUILayoutUtility.GetRect(15, 15);
        EditorGUI.DrawRect(colorRect, color);
        EditorGUILayout.LabelField(label, EditorStyles.label);
        EditorGUILayout.EndHorizontal();
    }
    
    private void OnTreeSelectionChanged(List<int> selectedIds)
    {
        if (selectedIds.Count > 0)
        {
            var selectedItem = _treeView.FindItemById(selectedIds[0]);
            _selectedNode = selectedItem?.Data;
        }
        else
        {
            _selectedNode = null;
        }
    }
    
    private void BuildDataStructures(OptimiserReport report)
    {
        if (report.sectionInfos != null)
        {
            var rootInfo = report.sectionInfos.FirstOrDefault(t => !t.section.Contains('/'));
            _rootTimeNode = new TimeNode(rootInfo.section, rootInfo.section)
            {
                Time = rootInfo.time,
                Description = rootInfo.description,
            };

            foreach (var timeInfo in report.sectionInfos)
            {
                if (timeInfo.section == rootInfo.section || string.IsNullOrEmpty(timeInfo.section)) continue;
                
                var parts = timeInfo.section.Split('/');
                var currentNode = _rootTimeNode;

                for (int i = 1; i < parts.Length; i++)
                {
                    var part = parts[i];
                    if (!currentNode.Children.TryGetValue(part, out var childNode))
                    {
                        var fullPath = string.Join("/", parts.Take(i + 1));
                        childNode = new TimeNode(part, fullPath) { Parent = currentNode };
                        currentNode.Children[part] = childNode;
                    }
                    currentNode = childNode;
                }
                
                currentNode.Time = timeInfo.time;
                currentNode.Description = timeInfo.description;
            }
        }

        GenerateOtherNodes(_rootTimeNode);
        
        _treeState ??= new TreeViewState();
        _treeView = new TimeTreeView(_treeState, _rootTimeNode);
        _treeView.OnSelectionChanged += OnTreeSelectionChanged;
        _treeView.Reload();
    }

    private void GenerateOtherNodes(TimeNode node)
    {
        foreach (var child in node.Children.Values.ToList())
            GenerateOtherNodes(child);

        if (node.Time <= 0.1f || node.Children.Count == 0) 
            return;
        
        float childrenTimeSum = node.Children.Values.Sum(c => c.Time);
        float otherTime = node.Time - childrenTimeSum;

        if (otherTime <= 0.1f) 
            return;
        
        var otherNode = new TimeNode("Other", $"{node.FullPath}/Other")
        {
            Time = otherTime,
            Parent = node,
            Description = "Time spent in this task but not in any of its defined sub-tasks."
        };
        node.Children["Other"] = otherNode;
    }

    /// <summary>
    /// Data structure with simplified Time property.
    /// </summary>
    private class TimeNode
    {
        public string Name { get; }
        public string FullPath { get; }
        public string Description { get; set; }
        public float Time { get; set; } // Represents inclusive time
        public TimeNode Parent { get; set; }
        public Dictionary<string, TimeNode> Children { get; } = new();

        public TimeNode(string name, string fullPath)
        {
            Name = name;
            FullPath = fullPath;
        }
    }

    private class TimeTreeView : BaseReportTreeView<TimeNode>
    {
        public TimeTreeView(TreeViewState state, TimeNode root) : base(state, root)
        {
            showAlternatingRowBackgrounds = true;
            Reload();
        }

        protected override TreeViewItem BuildRoot()
        {
            // Use the generic ReportTreeViewItem instead of a custom one
            var root = new ReportTreeViewItem<TimeNode> { id = 0, depth = -1, displayName = "Root", Data = rootData };

            var rootItem = new ReportTreeViewItem<TimeNode>
            {
                id = rootData.FullPath.GetHashCode(),
                depth = 0,
                displayName = $"{rootData.Name} ({rootData.Time:F2}s)",
                Data = rootData
            };
            root.AddChild(rootItem);
        
            AddChildrenRecursive(rootData, rootItem);
        
            SetupDepthsFromParentsAndChildren(root);
            return root;
        }

        private void AddChildrenRecursive(TimeNode node, TreeViewItem parent)
        {
            var sortedChildren = node.Children.Values.OrderByDescending(n => n.Time);
            foreach (var childNode in sortedChildren)
            {
                var item = new ReportTreeViewItem<TimeNode>
                {
                    id = childNode.FullPath.GetHashCode(),
                    // depth = parent.depth + 1, // depth is set automatically by SetupDepthsFromParentsAndChildren
                    displayName = $"{childNode.Name} ({childNode.Time:F2}s)",
                    Data = childNode
                };
                parent.AddChild(item);
                AddChildrenRecursive(childNode, item);
            }
        }
    }
}