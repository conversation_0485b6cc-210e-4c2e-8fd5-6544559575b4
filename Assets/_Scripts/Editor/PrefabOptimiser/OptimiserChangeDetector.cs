using System;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

public class OptimiserChangeDetector : AssetPostprocessor
{
    private static List<(string, string)> m_optimisedFolders = new();
    private static bool m_processingAutoOptimise;

    static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets,
        string[] movedFromAssetPaths)
    {
        if (PrefabOptimiser.IsInProgress())
            return; // Don't process if an optimisation is already in progress
        
        GatherOptimisedFolders();
        
        var relevantImports = new List<string>();
        var relevantDeletes = new List<string>();
        var relevantMoves = new List<(string, string)>();
        
        foreach (var assetPath in importedAssets)
        {
            if (IsRelevantAsset(assetPath))
                relevantImports.Add(assetPath);
        }

        foreach (var assetPath in deletedAssets)
        {
            if (IsRelevantAsset(assetPath))
                relevantDeletes.Add(assetPath);
        }

        for (int i = 0; i < movedAssets.Length; ++i)
        {
            var assetPath = movedAssets[i];
            var fromPath = movedFromAssetPaths[i];
            if (IsRelevantAsset(assetPath) || IsRelevantAsset(fromPath))
                relevantMoves.Add((fromPath, assetPath));
        }

        bool folderDirty = false;
        foreach (var (from, to) in m_optimisedFolders)
        {
            var infoPath = Path.Combine(from, "optimiser.info");
            if (!File.Exists(infoPath))
            {
                folderDirty = true;
                continue; // This folder has not been optimised yet, i.e. is already dirty
            }

            var lines = File.ReadAllLines(infoPath);
            var dependencies = new Dictionary<string, string>();
            foreach (var line in lines)
            {
                var parts = line.Split(',', StringSplitOptions.RemoveEmptyEntries);
                dependencies[parts[0]] = line;
            }
            
            bool hasSrcChanged = false, hasDstChanged = false;
            foreach (var assetPath in relevantImports)
            {
                if (dependencies.TryGetValue(assetPath, out var line))
                {
                    var parts = line.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    parts[2] = PrefabOptimiser.GetContentHash(assetPath);
                    dependencies[assetPath] = string.Join(',', parts);
                    hasSrcChanged = true;
                }
                else if (assetPath.StartsWith(from))
                {
                    dependencies[assetPath] = $"{assetPath},0,{PrefabOptimiser.GetContentHash(assetPath)}";
                    hasSrcChanged = true;
                }
                else if (assetPath.StartsWith(to))
                    hasDstChanged = true;
            }
            foreach (var assetPath in relevantDeletes)
            {
                if (dependencies.TryGetValue(assetPath, out var line))
                {
                    var parts = line.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    parts[2] = "0";
                    dependencies[assetPath] = string.Join(',', parts);
                    hasSrcChanged = true;
                }
                else if (assetPath.StartsWith(to))
                    hasDstChanged = true;
            }
            foreach (var (fromPath, toPath) in relevantMoves)
            {
                var fromFrom = fromPath.StartsWith(from);
                if (Path.GetExtension(fromPath) == ".prefab" && fromFrom != toPath.StartsWith(from))
                {
                    if (fromFrom)
                    {
                        var parts = dependencies[fromPath].Split(',', StringSplitOptions.RemoveEmptyEntries);
                        parts[2] = "0";
                        dependencies[fromPath] = string.Join(',', parts);
                        hasSrcChanged = true;
                    }
                    else
                    {
                        dependencies[toPath] = $"{toPath},0,{PrefabOptimiser.GetContentHash(toPath)}";
                        hasSrcChanged = true;
                    }
                }
                else if (fromPath.StartsWith(to) != toPath.StartsWith(to))
                    hasDstChanged = true;
            }

            if ((hasSrcChanged && !hasDstChanged)) // || (hasDstChanged && !KitBashOptimiseManager.IsInProgress())
            {
                folderDirty = true;
                lines = new List<string>(dependencies.Values).ToArray();
                File.WriteAllLines(infoPath, lines);
            }
            else if (hasSrcChanged)
            {
                lines = new string[dependencies.Count];
                int index = 0;
                foreach (var kvp in dependencies)
                {
                    var parts = kvp.Value.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length < 3)
                        continue;
                    parts[1] = parts[2];
                    lines[index++] = string.Join(',', parts);
                }
                File.WriteAllLines(infoPath, lines);
            }
        }

        if (folderDirty)
            PrefabOptimiseController.StartTryRunAll(true);
    }

    static void GatherOptimisedFolders()
    {
        m_optimisedFolders.Clear();
        foreach (var config in PrefabOptimiseController.GetSplitConfigs())
            m_optimisedFolders.Add((config.directoryFrom, config.directoryTo));
    }

    static bool IsRelevantAsset(string assetPath) => PrefabOptimiseController.IsRelevantAsset(assetPath);
}