using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using static KitBashOptimiseController;
using Object = UnityEngine.Object;

public class KitBashOptimiseWindow : EditorWindow
{
    private static int selectedConfigIndex;
    private Vector2 scrollPos;
    private bool configExpanded;
    internal static ConfigurationDropdown activeDropdown;

    [MenuItem("Art Tools/Optimise Prefabs")]
    public static void ShowWindow() => GetWindow<KitBashOptimiseWindow>("Optimise Prefabs");

    private void OnGUI()
    {
        float origLabel = EditorGUIUtility.labelWidth;
        EditorGUIUtility.labelWidth = 180;
        DrawHeader();
        EditorGUILayout.Space(10);
        DrawBody();
        EditorGUIUtility.labelWidth = origLabel;
    }

    #region Header & Body

    private void DrawHeader()
    {
        EditorGUILayout.Space(10);
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Kit Bash Optimiser", HeaderStyle());

        if (IsRunning)
        {
            var prev = GUI.backgroundColor;
            GUI.backgroundColor = Color.red;
            if (GUILayout.Button("Force Stop", GUILayout.Width(80))) ForceStop();
            GUI.backgroundColor = prev;
        }
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.Space(5);
        DrawLine();
        EditorGUILayout.Space(15);
    }

    private void DrawBody()
    {
        DrawRunAll();
        EditorGUILayout.Space(10);

        scrollPos = EditorGUILayout.BeginScrollView(scrollPos);

        DrawForceToggle();
        EditorGUILayout.Space(5);
        DrawAutoToggle();
        EditorGUILayout.Space(10);

        DrawConfigurationSelector();
        EditorGUILayout.Space(15);

        if (GUILayout.Button("Add Folder as Multiple Runs (Subfolders)", GUILayout.Height(30)))
            AddFolder(true);
        EditorGUILayout.Space(10);
        if (GUILayout.Button("Add Folder as Single Run", GUILayout.Height(30)))
            AddFolder(false);
        EditorGUILayout.Space(15);

        var config = GetNthConfig(ref selectedConfigIndex, out var has);
        if (has)
        {
            DrawConfiguration(config);
            EditorGUILayout.Space(15);
        }
        DrawActionButtons();
        EditorGUILayout.EndScrollView();
    }

    private void DrawForceToggle()
    {
        var prev = forceUpdate;
        DrawToggle(ref forceUpdate, "Force Update:");
        if (forceUpdate && !prev)
            autoOptimise = false;
    }
    
    private void DrawAutoToggle()
    {
        var prev = autoOptimise;
        bool curr = prev;
        DrawToggle(ref curr, "Auto-optimise:");
        if (curr != prev)
        {
            if (curr)
            {
                forceUpdate = false;
                StartTryRunAll(true);
            }
            autoOptimise = curr;
        }
    }

    #endregion
    
    #region Input Collection
    
    private static void AddFolder(bool _superFolder)
    {
        string inputFolder = EditorUtility.OpenFolderPanel("Select Input Folder", c_DefaultInputFolder, "");
        if (string.IsNullOrEmpty(inputFolder))
            return;

        string outputFolder = EditorUtility.OpenFolderPanel("Select Output Folder", c_DefaultOutputFolder, "");
        if (string.IsNullOrEmpty(outputFolder))
            return;

        selectedConfigIndex = AddConfig(inputFolder, outputFolder, _superFolder);
    }

    private void SaveConfiguration()
    {
        string path = EditorUtility.OpenFilePanel("Save Configurations", c_ConfigFolder, "json");
        if (!string.IsNullOrEmpty(path))
            WriteOutConfig(path);
    }
    
    private void LoadConfiguration()
    {
        string path = EditorUtility.OpenFilePanel("Load Configurations", c_ConfigFolder, "json");
        if (!string.IsNullOrEmpty(path))
        {
            ReadInConfig(path);
            selectedConfigIndex = 0;
        }
    }
    
    #endregion

    #region Configuration Section

    private void DrawConfigurationSelector()
    {
        EditorGUILayout.BeginHorizontal();
        {
            var configs = GetConfigs() ?? new();
            bool haveConfigsChanged = false;
            if (configs.Count > 0)
            {
                string currentName = selectedConfigIndex < configs.Count ? configs[selectedConfigIndex].name : "Select...";

                Rect dropdownRect = GUILayoutUtility.GetRect(new GUIContent(currentName), EditorStyles.popup, GUILayout.ExpandWidth(true));

                if (EditorGUI.DropdownButton(dropdownRect, new GUIContent(currentName), FocusType.Keyboard))
                {
                    if (activeDropdown != null)
                    {
                        activeDropdown.Close();
                        activeDropdown = null;
                    }
                    else
                    {
                        Vector2 dropdownPosition = GUIUtility.GUIToScreenPoint(new Vector2(dropdownRect.x, dropdownRect.yMax));
                        activeDropdown = ConfigurationDropdown.Show(dropdownPosition, dropdownRect.width, configs, selectedConfigIndex,
                            index => { selectedConfigIndex = index; activeDropdown = null; });
                    }
                }

                if (selectedConfigIndex < configs.Count)
                {
                    var wasEnabled = configs[selectedConfigIndex].enabled;
                    var enabled = EditorGUILayout.Toggle(configs[selectedConfigIndex].enabled, GUILayout.Width(20));
                    configs[selectedConfigIndex].enabled = enabled;
                    if (wasEnabled != enabled)
                        haveConfigsChanged = true;
                }
            }
            else
            {
                EditorGUILayout.LabelField("No runs :|", GUILayout.ExpandWidth(true));
            }

            using (new EditorGUI.DisabledScope(configs.Count == 0))
            {
                if (GUILayout.Button("-", GUILayout.Width(30)))
                {
                    configs.RemoveAt(selectedConfigIndex);
                    selectedConfigIndex = Mathf.Clamp(selectedConfigIndex, 0, configs.Count - 1);
                    haveConfigsChanged = true;
                }
            }
            
            if (haveConfigsChanged)
                SetConfigs(configs);
        }
        EditorGUILayout.EndHorizontal();
    }
    
    private void DrawConfiguration(Configuration cfg)
    {
        configExpanded = EditorGUILayout.Foldout(configExpanded, "Configuration Settings");
        if (!configExpanded) return;
        EditorGUI.indentLevel++;

        cfg.name = EditorGUILayout.TextField("Name:", cfg.name);
        DrawDirectoryField("Input Directory:", ref cfg.directoryFrom);
        DrawDirectoryField("Output Directory:", ref cfg.directoryTo);
        using (new EditorGUILayout.VerticalScope(GUI.skin.box))
        {
            EditorGUILayout.HelpBox(
                cfg.superFolder ? "Each subfolder atlas separately" : "All assets atlas together",
                MessageType.Info);
            EditorGUILayout.BeginHorizontal();
            cfg.superFolder = EditorGUILayout.Toggle(cfg.superFolder, GUILayout.Width(20));
            EditorGUILayout.LabelField("Process Subfolders Separately");
            EditorGUILayout.EndHorizontal();
        }

        cfg.atlasName = EditorGUILayout.TextField("Atlas Name:", cfg.atlasName);
        cfg.enabled = EditorGUILayout.Toggle("Enabled:", cfg.enabled);
        EditorGUILayout.Space(10);

        DrawObjectListField("Target Scenes to Update:", cfg.targetScenes, typeof(SceneAsset));
        EditorGUILayout.Space(5);
        DrawObjectListField("Target Prefabs to Update:", cfg.targetPrefabs, typeof(GameObject));

        EditorGUI.indentLevel--;
    }

    private void DrawDirectoryField(string label, ref string path)
    {
        EditorGUILayout.LabelField(label, EditorStyles.boldLabel);
        EditorGUILayout.HelpBox("Drag folder from Project window here", MessageType.None);
        var drop = GUILayoutUtility.GetRect(0, 50, GUILayout.ExpandWidth(true));
        var ind = EditorGUI.IndentedRect(drop);
        GUI.Box(ind, string.IsNullOrEmpty(path) ? "Drag & Drop Folder Here" : path, GUI.skin.box);
        DrawBorder(ind);

        var evt = Event.current;
        if (ind.Contains(evt.mousePosition))
        {
            if (evt.type == EventType.DragUpdated)
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                evt.Use();
            }
            else if (evt.type == EventType.DragPerform)
            {
                DragAndDrop.AcceptDrag();
                if (DragAndDrop.paths.Length > 0 && AssetDatabase.IsValidFolder(DragAndDrop.paths[0]))
                    path = DragAndDrop.paths[0];
                evt.Use();
            }
        }
    }

    private static void DrawObjectListField<T>(string label, List<T> list, System.Type type) where T : Object
    {
        EditorGUILayout.LabelField(label, EditorStyles.boldLabel);
        EditorGUI.indentLevel++;
        for (int i = 0; i < list.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            list[i] = (T)EditorGUILayout.ObjectField(list[i], type, false);
            if (GUILayout.Button("X", GUILayout.Width(20))) { list.RemoveAt(i); i--; }
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.BeginHorizontal();
        GUILayout.Space(EditorGUI.indentLevel * 10);
        if (GUILayout.Button($"Add {type.Name}", GUILayout.Width(150)))
            list.Add(null);
        EditorGUILayout.EndHorizontal();
        EditorGUI.indentLevel--;
    }

    #endregion

    #region UI Helpers

    public static void ToolbarWarningButton()
    {
        Color originalColor = GUI.backgroundColor;
        GUI.backgroundColor = new Color(1.0f, 0.2f, 0.2f);

        EditorGUILayout.BeginHorizontal();
        GUILayout.Space(20);
        if (GUILayout.Button(new GUIContent("⚠ Optimise Now! ⚠", "Click to run the prefab optimiser"), warningStyle))
            StartTryRunAll();
        GUILayout.Space(20);
        EditorGUILayout.EndHorizontal();

        GUI.backgroundColor = originalColor;
    }

    private void DrawRunAll()
    {
        bool can = CanRun(out var msg);
        using (new EditorGUI.DisabledScope(!can))
        {
            var prev = GUI.backgroundColor;
            GUI.backgroundColor = can ? new Color(0.2f, 0.7f, 0.1f) : Color.gray;
            if (GUILayout.Button("Optimise!", ActionButtonStyle())) 
                StartTryRunAll();
            GUI.backgroundColor = prev;
        }
        if (!can) EditorGUILayout.HelpBox(msg, MessageType.Warning);
    }

    private void DrawToggle(ref bool val, string label)
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(label, GUILayout.Width(150));
        val = EditorGUILayout.Toggle(val, GUILayout.Width(30));
        EditorGUILayout.EndHorizontal();
    }

    private void DrawLine()
    {
        var r = GUILayoutUtility.GetRect(1, 1);
        r.xMin = 0; r.xMax = EditorGUIUtility.currentViewWidth;
        EditorGUI.DrawRect(r, new Color(0.4f, 0.4f, 0.4f, 0.5f));
    }

    private void DrawBorder(Rect r)
    {
        var col = GUI.color;
        GUI.color = Color.grey;
        float t = 2;
        GUI.DrawTexture(new Rect(r.x, r.y, r.width, t), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(r.x, r.yMax - t, r.width, t), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(r.x, r.y, t, r.height), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(r.xMax - t, r.y, t, r.height), Texture2D.whiteTexture);
        GUI.color = col;
    }

    private void DrawActionButtons()
    {
        if (GUILayout.Button("Save Configuration")) SaveConfiguration();
        if (GUILayout.Button("Load Configuration")) LoadConfiguration();
        if (GUILayout.Button("Clear Configuration")) ClearConfiguration();
    }

    #endregion

    #region Styles

    private GUIStyle HeaderStyle() => new (EditorStyles.largeLabel)
    {
        fontSize = 18,
        fontStyle = FontStyle.Bold,
        alignment = TextAnchor.MiddleCenter
    };

    private GUIStyle ActionButtonStyle() => new (GUI.skin.button)
    {
        fontSize = 14,
        fontStyle = FontStyle.Bold,
        padding = new RectOffset(20, 20, 8, 8)
    };
    
    private static GUIStyle warningStyle => new(EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).button)
    {
        normal = { textColor = Color.yellow},
        active = { textColor = Color.yellow},
        hover = { textColor = Color.white},
        fontStyle = FontStyle.Bold,
        padding = new RectOffset(10, 10, 2, 2)
    };

    #endregion

    private void OnDisable() => ForceStop();
}

public class ConfigurationDropdown : EditorWindow
{
    private List<Configuration> configurations;
    private int selectedIndex;
    private System.Action<int> onSelectionChanged;
    private Vector2 scrollPosition;

    public static ConfigurationDropdown Show(Vector2 position, float width, List<Configuration> configs, int selected, System.Action<int> onChanged)
    {
        var window = CreateInstance<ConfigurationDropdown>();
        window.configurations = configs;
        window.selectedIndex = selected;
        window.onSelectionChanged = onChanged;

        float height = Mathf.Min(configs.Count * 20 + 60, 300);
        float dropdownWidth = Mathf.Max(width, 200);

        window.position = new Rect(position.x, position.y, dropdownWidth, height);
        window.ShowPopup();
        window.Focus();

        return window;
    }

    private void OnGUI()
    {
        if (Event.current.type == EventType.MouseDown && !new Rect(0, 0, position.width, position.height).Contains(Event.current.mousePosition))
        {
            Close();
            return;
        }

        DrawDropdownBackground();

        GUILayout.BeginArea(new Rect(2, 2, position.width - 4, position.height - 4));
        EditorGUILayout.BeginVertical();

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        bool haveConfigsChanged = false;
        for (int i = 0; i < configurations.Count; i++)
        {
            var config = configurations[i];

            EditorGUILayout.BeginHorizontal();

            bool wasEnabled = config.enabled;
            config.enabled = EditorGUILayout.Toggle(config.enabled, GUILayout.Width(20));
            haveConfigsChanged |= wasEnabled != config.enabled;

            bool isSelected = i == selectedIndex;
            var style = isSelected ? EditorStyles.boldLabel : EditorStyles.label;

            if (GUILayout.Button(config.name, style, GUILayout.ExpandWidth(true)))
            {
                selectedIndex = i;
                onSelectionChanged?.Invoke(i);
                Close();
            }

            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.EndScrollView();

        EditorGUILayout.Space(5);
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("All On", EditorStyles.miniButtonLeft))
        {
            foreach (var config in configurations)
                config.enabled = true;
            haveConfigsChanged = true;
        }

        if (GUILayout.Button("All Off", EditorStyles.miniButtonRight))
        {
            foreach (var config in configurations)
                config.enabled = false;
            haveConfigsChanged = true;
        }
        
        if (haveConfigsChanged)
            SetConfigs(configurations);

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();
        GUILayout.EndArea();
    }

    private void DrawDropdownBackground()
    {
        EditorGUI.DrawRect(new Rect(0, 0, position.width, position.height), new Color(0.8f, 0.8f, 0.8f, 1f));

        Color borderColor = new Color(0.4f, 0.4f, 0.4f, 1f);

        EditorGUI.DrawRect(new Rect(0, 0, position.width, 1), borderColor);
        EditorGUI.DrawRect(new Rect(0, position.height - 1, position.width, 1), borderColor);
        EditorGUI.DrawRect(new Rect(0, 0, 1, position.height), borderColor);
        EditorGUI.DrawRect(new Rect(position.width - 1, 0, 1, position.height), borderColor);

        EditorGUI.DrawRect(new Rect(1, 1, position.width - 2, position.height - 2), EditorGUIUtility.isProSkin ? new Color(0.22f, 0.22f, 0.22f, 1f) : new Color(0.76f, 0.76f, 0.76f, 1f));
    }

    private void OnLostFocus()
    {
        Close();
    }

    private void OnDestroy()
    {
        if (KitBashOptimiseWindow.activeDropdown == this)
            KitBashOptimiseWindow.activeDropdown = null;
    }
}

