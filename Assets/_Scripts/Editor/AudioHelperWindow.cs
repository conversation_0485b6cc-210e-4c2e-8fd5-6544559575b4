using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using Unity.Mathematics;
using UnityEditor.IMGUI.Controls;

public class AudioHelperWindow : EditorWindow
{
	void OnEnable() {
		Refresh();
		InitPicker();
	}

	TreeViewState m_treeViewState;
	void InitPicker()
	{
		var multiColumnHeaderState = AkWwiseTreeView.CreateDefaultMultiColumnHeaderState();
		var multiColumnHeader = new UnityEditor.IMGUI.Controls.MultiColumnHeader(multiColumnHeaderState);
		m_treeView = new AkWwiseTreeView(m_treeViewState, multiColumnHeader, AkWwiseProjectInfo.GetTreeData());
		//m_treeView.SetDoubleClickFunction(PlayPauseItem);
		//m_treeView.dirtyDelegate = RequestRepaint;
	}

	void Refresh()
	{

	}

	public static string Def(string _s)
	{
		if (string.IsNullOrEmpty(_s)) return "-";
		return _s;
	}

	public static (string, bool) Diff(string _s, string _s2, bool _ownerNull)
	{
		if (_ownerNull || _s2 == _s) return (Def(_s), false);
		if (string.IsNullOrEmpty(_s)) return ($"-> {Def(_s2)}", true);
		return ($"{Def(_s)} -> {Def(_s2)}", true);
	}
	
	private static string GetAuthoredPath(string _path)
	{
		foreach (var config in PrefabOptimiseController.GetSplitConfigs())
		{
			if (_path.StartsWith(config.directoryTo))
				return $"{config.directoryFrom}{_path[config.directoryTo.Length..]}";
		}
		return _path;
	}

	public static AkWwiseTreeView m_treeView;

	private Vector2 m_scrollPosition;
	private float m_scrollViewHeight;
	void OnGUI()
	{
		var buttonWidth = 28;
		var buttonsWidth = buttonWidth * 2;
		var width = position.width - 44 - buttonsWidth;
		var nameWidth = width * .24f;
		var classWidth = width * .16f;
		var outputAudioWidth = width * .2f;
		var workAudioWidth = width * .2f;
		var workBuildingAudioWidth = width * .2f;
		
		GUILayout.BeginHorizontal();
		GUILayout.Label("Block", GUILayout.Width(nameWidth));
		GUILayout.Label("Type", GUILayout.Width(classWidth));
		GUILayout.Label("Output", GUILayout.Width(outputAudioWidth));
		GUILayout.Label("Work", GUILayout.Width(workAudioWidth));
		GUILayout.Label("Work(B)", GUILayout.Width(workBuildingAudioWidth));
		GUILayout.EndHorizontal();
		
		var blockInfos = NGBlockInfo.s_allBlocks;
		if (blockInfos == null || blockInfos.Count == 0)
		{
			NGKnack.SetEditorMe();
			NGBlockInfo.LoadInfo();
			MAComponentInfo.LoadInfo();
			blockInfos = NGBlockInfo.s_allBlocks;
		}
		m_scrollPosition = GUILayout.BeginScrollView(m_scrollPosition);
		foreach (var kvp in blockInfos)
		{
			var info = kvp.Value;
			var components = info.GetComponentInfos();
			foreach (var comp in components)
			{
				if (comp.m_classType.IsSubclassOf(typeof(BCActionBase)))// && comp.m_classType != typeof(BCOrderBoard))
				{
					var path = $"Assets/Resources/_Prefabs/_Blocks/{info.m_prefabPath}.prefab";
					var go = AssetDatabase.LoadAssetAtPath<GameObject>(GetAuthoredPath(path));
					if (go == null)
					{
					}
					else
					{
						var sel = Selection.activeGameObject;
						var block = go.GetComponent<Block>();
						Block selBlock = null;
						if (sel != null)
						{
							var selCmp = sel.GetComponentInChildren(comp.m_classType);
							if (selCmp != null)
							{
								selBlock = selCmp.GetComponent<Block>();
								if (selBlock != null && selBlock.m_blockInfoID != block.m_blockInfoID)
									selBlock = null;
							}
						}

						var baseColour = selBlock != null ? Color.yellow : Color.white; 
						GUI.color = baseColour;
						var (workAudio, workDiff) = Diff(block.m_animatedAudio.Name, selBlock?.m_animatedAudio.Name, selBlock == null);
						var (workBuildingAudio, workdBuildingDiff) = Diff(block.m_animatedBuildingAudio.Name, selBlock?.m_animatedBuildingAudio.Name, selBlock == null);
						var (outputAudio, outputDiff) = Diff(block.m_createOutputAudioEvent?.Name, selBlock?.m_createOutputAudioEvent?.Name, selBlock == null);
						GUILayout.BeginHorizontal();
						if (GUILayout.Button(info.m_prefabName, GUILayout.Width(nameWidth)))
							Selection.activeObject = go;
						GUILayout.Label(comp.m_classType.Name, GUILayout.Width(classWidth));
						GUI.color = outputDiff ? Color.red : baseColour;
						GUILayout.Label(outputAudio, GUILayout.Width(outputAudioWidth));
						GUI.color = workDiff ? Color.red : baseColour;
						GUILayout.Label(workAudio, GUILayout.Width(workAudioWidth));
						GUI.color = workdBuildingDiff ? Color.red : baseColour;
						GUILayout.Label(workBuildingAudio, GUILayout.Width(workBuildingAudioWidth));
						GUI.color = Color.white;
						if (workDiff || workdBuildingDiff || outputDiff)
						{
							if (GUILayout.Button(">I", GUILayout.Width(buttonWidth)))
							{
								selBlock.m_animatedAudio = block.m_animatedAudio;
								selBlock.m_animatedBuildingAudio = block.m_animatedBuildingAudio;
								selBlock.m_createOutputAudioEvent.WwiseObjectReference = block.m_createOutputAudioEvent.WwiseObjectReference;
							}
							if (GUILayout.Button(">P", GUILayout.Width(buttonWidth)))
							{
								block.m_animatedAudio = selBlock.m_animatedAudio;
								block.m_animatedBuildingAudio = selBlock.m_animatedBuildingAudio;
								block.m_createOutputAudioEvent.WwiseObjectReference = selBlock.m_createOutputAudioEvent.WwiseObjectReference;
								PrefabUtility.SavePrefabAsset(go);
							}
						}
						GUILayout.EndHorizontal();
					}
				}
			}
		}
		GUILayout.EndScrollView();
	}

	[MenuItem("Window/Audio Helper")]
	static void ShowWindow()
	{
		var window = GetWindow<AudioHelperWindow>();
		window.titleContent = new GUIContent("Audio Helper");
		window.Show();
	}
}
