using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using static CameraRenderSettings;
using Random = UnityEngine.Random;

public class MAAnimal : MACharacterBase, ICharacterObjectInteract
{
    public const string c_pissAnimation = "Piss";
    public const string c_pooAnimation = "Poo";
    public const string c_characterKickAnimation = "Kick";
    public const string c_animalKickedAnimation = "Kicked";
    public float m_kickVelocity = 10;
    
    protected override float NavigationCorridor => 0f;	
    protected override float[] NavigationCosts
    {
        get 
        {//can we add a human, animal, creature column into creatureinfo?
            bool isCalm = /*IsHuman && */IsFleeing == false && IsInAttackState == false;// && m_state == STATE.MA_MOVE_TO_INSIDE_BUILDING;
            return isCalm ? GlobalData.s_animalCosts : GlobalData.s_threatCosts;
        }
    }
    
    public override bool IsNotBusy => CharacterUpdateState.State != CharacterStates.Spawn && (IsOnPatrol || IsRoamingFreely || IsFollowing) && m_targetObject == null;

    public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() => MAAnimalStateLibrary.StateLibrary;
    
    public override bool IsHuman => false;

    public override string InitialState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_initialState) ? CharacterStates.Idle : m_gameState.m_initialState;
        set => m_gameState.m_initialState = value;
    }
    
    public override string DefaultState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_defaultState) ? CharacterStates.Idle : m_gameState.m_defaultState;
        set => m_gameState.m_defaultState = value;
    }

    [Header("MAAnimal")]
    [SerializeField] private string[] m_allAnimalAnims = {"Bark", "Poo", "Piss", "Eat"};
    public AkEventHolder m_interactSound;
    public GameObject m_interactVFX;
    
    public float AutoInteractTime => 0;
    
    [Serializable]
    public class AnimalAnim
    {
        [Dropdown("m_allAnimalAnims")] public string name;
        public float delay;

        public static implicit operator AnimalAnim((string, float) nd)
        {
            return new() {name = nd.Item1, delay = nd.Item2};
        }
    }

    public AnimalAnim[] m_sequence = {("Bark", 2f), ("Poo", 2f), ("Piss", 2f), ("Eat", 2f)};
    public bool m_usingSequence = false;
    private int m_sequenceIndex = 0;
    private bool m_readyForNextAnim = true;
    private MACreatureInfo m_info;

    [FormerlySerializedAs("m_dogVision")] [SerializeField]
    private VisionMode _possessVision = new(0, -1000, 70f);

    public override VisionMode VisionMode => _possessVision;

    protected override void Update()
    {
        base.Update();
    }
    
    public override void DoPossessedAction()
    {
        if (m_readyForNextAnim)
            StartNextAnim(true);
    }

    private void StartNextAnim(bool _allowRandomAnim)
    {
        m_readyForNextAnim = false;
        if (m_usingSequence)
        {
            m_sequenceIndex = ++m_sequenceIndex % m_sequence.Length;
            PlayAnim(m_sequence[m_sequenceIndex].name, (a, b) => { OnSequencedAnimFinished(a, b); });
            return;
        }

        if (_allowRandomAnim)
        {
            DoRandom();
        }
    }

    private void OnSequencedAnimFinished(MAAnimationSet.AnimationParams _animInfo, bool _interrupted)
    {
        Utility.After(m_sequence[m_sequenceIndex].delay, () => m_readyForNextAnim = true);
    }

    private void DoRandom()
    {
        var anim = m_allAnimalAnims[Random.Range(0, m_allAnimalAnims.Length)];
        PlayAnim(anim, (a, b) => { OnRandomAnimFinished(a, b); });
    }

    private void OnRandomAnimFinished(MAAnimationSet.AnimationParams _animInfo, bool _interrupted)
    {
        m_readyForNextAnim = true;
    }

    private void AddToLists()
    {
        if (!NGManager.Me.m_MAAnimalList.Contains(this))
            NGManager.Me.m_MAAnimalList.Add(this);
    }

    public override void Activate(MACreatureInfo _info, MABuilding _optionalOwner, bool _init, Vector3 _position, 
        Vector3 _rotation, bool _addToCharacterLists = true)
    {
        base.Activate(_info, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
        m_info = _info;
        if (_addToCharacterLists)
        {
            AddToLists();
        }
        
        Health = 1f;
        
        // warning, we are setting the character to immortal here because we have no clear indication as to what to do with chickens, the dog, etc when killed (by boulder, spells etc)
        //CharacterGameState.m_immortal = true;

        m_gameState.m_speed = m_gameState.m_walkSpeed;
        
        StartNextAnim(false);
        
        m_nav.SetNavCosts(GlobalData.s_animalCosts);
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        if (GameManager.Me != null)
        {
            GameManager.Me.m_state.m_minorCharacterTypes.Remove(m_gameState);
        }
        if (NGManager.Me != null)
        {
            NGManager.Me.m_MAAnimalList.Remove(this);
        }
    }

    private void ShowInteractVFX()
    {
        if (m_interactVFX == null) return;
        Instantiate(m_interactVFX, transform);
    }

    // ICharacterObjectInteract
    public string InteractType => null;

    public virtual bool CanInteract(NGMovingObject _chr)
    {
        return false;
    }

    public virtual string GetInteractLabel(NGMovingObject _chr)
    {
        return "Kick";
    }

    public bool RunInteractSequence(List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    
    public void SetKickVelocity(float _kickVelocity)
    {
        m_kickVelocity = _kickVelocity;
    }

    public virtual void DoInteract(NGMovingObject _chr)
    {
        string pauseLabel = $"Kick{Time.frameCount}";
        _chr.m_nav.PushPause(pauseLabel);
        m_nav.enabled = false;
        
        AlignmentManager.Me.ApplyAction("KickAnimal", m_info.m_negativeInteractionAlignment, m_info.m_name);
        
        var toAnimal = transform.position - _chr.transform.position;
        GameObject selfObj = gameObject;
        _chr.LookAt((_chr.transform.position + toAnimal).GetXZ(), 480, () => {
            if(selfObj == null) return;
            m_interactSound?.Play(selfObj);
            _chr.PlaySingleAnimation(c_characterKickAnimation,  (b) => {
                _chr.m_nav.PopPause(pauseLabel);
            });
            const float c_kickedAtTime = .6f;
            var rb = GetComponentInChildren<Rigidbody>();
            float linearDampingBackup = rb.linearDamping;
            rb.linearDamping = 0f;
            this.DoAfter(c_kickedAtTime, () => {
                ShowInteractVFX();
                PhysicsMaterial backUpPhysMat1 = m_nav.Collider.sharedMaterial;
                PlayLoopAnimation(c_animalKickedAnimation, (b) => { });
                transform.LookAt(transform.position + toAnimal, Vector3.up);
                rb.detectCollisions = false;
                m_nav.Collider.material.dynamicFriction = 0.25f;
                var force = (toAnimal.normalized + Vector3.up * 2) * m_kickVelocity;
                rb.AddForce(force, ForceMode.Impulse);
                this.DoAfter(.1f, () => {
                    rb.detectCollisions = true;
                });
                int settled = 0;
                this.Do(() => {// && rbTr.position.y.Nearly(rb.transform.position.GroundPosition().y, 0.05f)
                    if (rb. linearVelocity.y.Nearly(0)) ++settled;
                    else settled = 0;
                    if (settled < 10) return true;
                    m_nav.Collider.material = backUpPhysMat1;
                    rb.linearDamping = linearDampingBackup;
                    // Settled on ground, back to normal
                    StopWorkerLoopAnimation();
                    m_nav.enabled = true;
                    return false;
                });
            });
        });
    }
    // End ICharacterObjectInteracts

    protected override void ApplyInitialCharacterState()
    {
        MACharacterStateFactory.ApplyInitialState(m_gameState.m_savedUpdateState, this);
    }
    
    protected override void InitialiseGameState()
    {
        string defaultState = DefaultState;
        string initialState = InitialState;
        
        base.InitialiseGameState();
		
        GameState_Character gameState = new GameState_Character();
        SetGameStateSaveData(gameState);
        GameManager.Me.m_state.m_minorCharacterTypes.Add(gameState);
		
        Transform tr = transform;
        Vector3 position = tr.position;
        Quaternion rotation = tr.rotation;

        gameState.m_id = m_ID;
        gameState.m_type = GetType().Name;
        gameState.m_creatureInfoName = m_creatureInfo.m_name;
        gameState.m_bodyType = FindBodyType();
        gameState.m_savedUpdateState = initialState;
        gameState.m_initialState = initialState;
        gameState.m_defaultState = defaultState;
        gameState.m_pos = position;
        gameState.m_rotation = rotation.eulerAngles;
        gameState.m_destPos = position;
        gameState.m_aliveDuration = 0f;
        gameState.m_health = m_creatureInfo.m_health;
        gameState.m_walkSpeed = m_creatureInfo.GetNewWalkingSpeed();
        gameState.m_attackSpeed = m_creatureInfo.GetNewAttackSpeed();
        gameState.m_spawnPos = position;
        gameState.m_characterExperience = new CharacterExperience();
    }
    
    public static MAAnimal Create(string _name, MACreatureInfo _info, Vector3 _pos, Quaternion? _rot = null)
    {
        MACharacterBase instance = MACreatureControl.Me.SpawnNewCreatureAtPosition(_info, _pos, null, null, _name, _rot?.eulerAngles ?? Vector3.zero);
        return instance as MAAnimal;
    }
    
    protected override bool StateMoveToPosition()
    {
        if (HasAgentArrived() == false) return false;
        m_nav.StopNavigation();
        m_insideMABuilding = null;
        SetState(STATE.IDLE);
        return true;
    }
}