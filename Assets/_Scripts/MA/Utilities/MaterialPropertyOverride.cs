using System;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor.SceneManagement;
#endif
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Serialization;

[RequireComponent(typeof(MeshRenderer))]
[ExecuteAlways]
public class MaterialPropertyOverride : MonoB<PERSON>aviour, IBatchWhenSame
{
    #if UNITY_EDITOR
    public bool EditorPreview { get; set; } = true;
#endif
    
    public int m_subMeshIndex;
    [FormerlySerializedAs("m_materialPropertyOverrides")] [HideInInspector] public List<PropertyOverride> m_propertyOverrides = new();
    
    [Serializable]
    public class PropertyOverride
    {
        public string m_name;
        public float m_overrideF;
        public Color m_overrideC;
        public Vector4 m_overrideV;
        public int m_overrideI;

        public static bool IsEq(Vector4 _a, Vector4 _b)
        {
            return IsEq(_a.x, _b.x) && IsEq(_a.y, _b.y) && IsEq(_a.z, _b.z) && IsEq(_a.w, _b.w);
        }
        
        public static bool IsEq(float _a, float _b)
        {
            const float epsilon = 1e-6f;
            if (_a.Equals(_b))
                return true;
            bool aDenormal = float.IsNaN(_a) || float.IsInfinity(_a);
            bool bDenormal = float.IsNaN(_b) || float.IsInfinity(_b);
            if (aDenormal || bDenormal)
                return aDenormal && bDenormal;
            float divisor = Math.Max(_a, _b);
            if (divisor <= 0)
                divisor = Math.Min(_a, _b);
            return Math.Abs(_a - _b) / divisor <= epsilon;
        }

        public override string ToString()
        {
            return $"{m_name}:{m_overrideF}F, {m_overrideC}C, {m_overrideV}V, {m_overrideI}I";
        }
    }

    private static readonly string[] s_blackListedProperties =
    {
        "_QueueControl"
    };
    
    public static void TryAdd(Material _old, Material _new, int _subMesh, GameObject _addTo)
    {
        var properties = new List<PropertyOverride>();
        var shader = _old.shader;
        var propCount = shader.GetPropertyCount();
        for (int i = 0; i < propCount; ++i)
        {
            var name = shader.GetPropertyName(i);
            if (s_blackListedProperties.Contains(name))
                continue;
            
            var type = shader.GetPropertyType(i);
            var id = shader.GetPropertyNameId(i);
            switch (type)
            {
                case ShaderPropertyType.Color:
                    var color = _old.GetColor(id);
                    if (PropertyOverride.IsEq(_new.GetColor(id), color))
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideC = color });
                    break;
                case ShaderPropertyType.Vector:
                    var vector = _old.GetVector(id);
                    if (PropertyOverride.IsEq(_new.GetVector(id), vector))
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideV = vector });
                    break;
                case ShaderPropertyType.Float:
                case ShaderPropertyType.Range:
                    var f = _old.GetFloat(id);
                    if (PropertyOverride.IsEq(_new.GetFloat(id), f))
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideF = f });
                    break;
                case ShaderPropertyType.Int:
                    var n = _old.GetInt(id);
                    if (_new.GetInt(id) == n)
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideI = n });
                    break;
            }
        }
        if (properties.Count == 0)
            return;

        var mpo = _addTo.AddComponent<MaterialPropertyOverride>();
        mpo.m_subMeshIndex = _subMesh;
        mpo.m_propertyOverrides = properties;
    }

    private void OnEnable()
    {
        #if UNITY_EDITOR
        if (EditorSceneManager.IsPreviewScene(gameObject.scene))
        {
            ApplyEditorPreview();
            return;
        }
        if (!Application.isPlaying)
            return;
        #endif
        try
        {
            var mr = GetComponent<MeshRenderer>();
            var mats = mr.materials;
            var mat = mats[m_subMeshIndex];
            foreach (var mp in m_propertyOverrides)
            {
                int propertyIndex = mat.shader.FindPropertyIndex(mp.m_name);
                if (propertyIndex == -1)
                {
                    Debug.LogError($"Property '{mp.m_name}' not found in shader '{mat.shader.name}' for submesh {m_subMeshIndex} on {transform.Path()}");
                    continue;
                }
                var type = mat.shader.GetPropertyType(propertyIndex);
                var id = mat.shader.GetPropertyNameId(propertyIndex);
                switch (type)
                {
                    case ShaderPropertyType.Color:
                        mat.SetColor(id, mp.m_overrideC);
                        break;
                    case ShaderPropertyType.Vector:
                        mat.SetVector(id, mp.m_overrideV);
                        break;
                    case ShaderPropertyType.Float:
                    case ShaderPropertyType.Range:
                        mat.SetFloat(id, mp.m_overrideF);
                        break;
                    case ShaderPropertyType.Int:
                        mat.SetInt(id, mp.m_overrideI);
                        break;
                    default:
                        Debug.LogError($"Unsupported property type {type} for '{mp.m_name}'");
                        break;
                }
            }
            mats[m_subMeshIndex] = mat;
            mr.materials = mats;
        }
        catch (Exception e)
        {
            Debug.LogException(e);
        }
    }

#if UNITY_EDITOR
    #region MatComparison
    
    public Material testAgainst;
    public bool test;

    private void Update()
    {
        if (test)
            TestAgainst(testAgainst);
        test = false;
    }
    
    private void TestAgainst(Material _other)
    {
        var mat = GetComponent<MeshRenderer>().sharedMaterials[m_subMeshIndex];
        var shader = mat.shader;
        var propCount = shader.GetPropertyCount();
        for (int i = 0; i < propCount; ++i)
        {
            var name = shader.GetPropertyName(i);
            var type = shader.GetPropertyType(i);
            var id = shader.GetPropertyNameId(i);
            switch (type)
            {
                case ShaderPropertyType.Color:
                    var color = mat.GetColor(id);
                    var otherColor = _other.GetColor(id);
                    if (otherColor == color)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {color}, other value is {otherColor}");
                    break;
                case ShaderPropertyType.Vector:
                    var vector = mat.GetVector(id);
                    var otherVector = _other.GetVector(id);
                    if (otherVector == vector)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {vector}, other value is {otherVector}");
                    break;
                case ShaderPropertyType.Float:
                case ShaderPropertyType.Range:
                    var f = mat.GetFloat(id);
                    var otherF = _other.GetFloat(id);
                    if ((otherF - f).IsZero())
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {f}, other value is {otherF}");
                    break;
                case ShaderPropertyType.Texture:
                    var hash = mat.GetTexture(id)?.imageContentsHash ?? new Hash128();
                    var otherHash = _other.GetTexture(id)?.imageContentsHash ?? new Hash128();
                    if (hash == otherHash)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {hash}, other value is {otherHash}");
                    break;
                case ShaderPropertyType.Int:
                    var n = mat.GetInt(id);
                    var otherN = _other.GetInt(id);
                    if (otherN == n)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {n}, other value is {otherN}");
                    break;
            }
        }
    }
    
    #endregion MatComparison

    #region EditorPreview
    
    public void ApplyEditorPreview()
    {
        var renderer = GetComponent<MeshRenderer>();
        renderer.SetPropertyBlock(null, m_subMeshIndex);
        if (renderer == null || m_propertyOverrides == null)
            return;

        
        if (!EditorPreview)
            return;

        if (renderer.sharedMaterials.Length <= m_subMeshIndex)
            return;

        var material = renderer.sharedMaterials[m_subMeshIndex];
        if (material == null || material.shader == null)
            return;

        var editorPreviewBlock = new MaterialPropertyBlock();
        foreach (var prop in m_propertyOverrides)
        {
            int propIndex = material.shader.FindPropertyIndex(prop.m_name);
            if (propIndex < 0)
                continue;

            var propType = material.shader.GetPropertyType(propIndex);
            var propId = material.shader.GetPropertyNameId(propIndex);

            switch (propType)
            {
                case ShaderPropertyType.Color:
                    editorPreviewBlock.SetColor(propId, prop.m_overrideC);
                    break;
                case ShaderPropertyType.Vector:
                    editorPreviewBlock.SetVector(propId, prop.m_overrideV);
                    break;
                case ShaderPropertyType.Float:
                case ShaderPropertyType.Range:
                    editorPreviewBlock.SetFloat(propId, prop.m_overrideF);
                    break;
                case ShaderPropertyType.Int:
                    editorPreviewBlock.SetInt(propId, prop.m_overrideI);
                    break;
            }
        }
        renderer.SetPropertyBlock(editorPreviewBlock, m_subMeshIndex);
    }

    void OnValidate()
    {
        if (EditorSceneManager.IsPreviewScene(gameObject.scene))
            ApplyEditorPreview();
    }
    
    #endregion
#endif
    
    public Component Component() => this;
    public bool IsApplicable(int _smi) => m_subMeshIndex == _smi;
    public int BatchHash()
    {
        int hash = m_subMeshIndex;
        if (m_propertyOverrides != null)
        {
            foreach (var prop in m_propertyOverrides)
            {
                hash = hash * 31 ^ (prop.m_name.GetHashCode() + 
                    prop.m_overrideF.GetHashCode() + 
                    prop.m_overrideC.GetHashCode() + 
                    prop.m_overrideV.GetHashCode() + 
                    prop.m_overrideI.GetHashCode());
            }
        }

        return hash;
    }
    public void OnBatch(Component _new)
    {
        ((MaterialPropertyOverride)_new).m_subMeshIndex = 0; //Combined meshes always have a single submesh
    }
}